import { BuildFromType, PassengerLifeAnimation } from "../../common/constant/Enums";
import EventType from "../../common/event/EventType";
import PassengerModel from "../../model/passenger/PassengerModel";
import TrainModel from "../../model/train/TrainModel";
import BuildObj from "../../model/train/common/BuildObj";
import { resHelper } from "../../common/helper/ResHelper";
import { MAX_ZINDEX } from "../../common/constant/Constant";
import { dropItemHelper } from "../../common/helper/DropItemHelper";
import DropItemObj from "../../model/train/common/DropItemObj";
import PassengerView from "../passenger/PassengerView";
import BuildCmpt from "../cmpt/build/BuildCmpt";
import CarriageModel from "../../model/train/common/CarriageModel";
import WaterCloudObj from "../../model/train/water/WaterCloudObj";
import WaterCloudCmpt from "../cmpt/build/water/WaterCloudCmpt";
import WaterModel from "../../model/train/water/WaterModel";
import CarriageGridEditView from "../_edit/CarriageGridEditView";
import { gameHelper } from "../../common/helper/GameHelper";
import BurstItem from "../../model/train/burst/BurstItem";
import DormBirdView from "../cmpt/build/dorm/DormBirdView";
import DormBirdModel from "../../model/train/dorm/DormBirdModel";

const { ccclass, property } = cc._decorator;

@ccclass
export default class TrainCarriage extends mc.BaseCmptCtrl {

    @property(cc.Node)
    facility: cc.Node = null

    @property({ type: CarriageGridEditView, tooltip: CC_DEV && '如需显示地图,把res/perfab/editor/carriage_grid放在根节点下' })
    myGrid: CarriageGridEditView = null

    @property(cc.Node)
    top: cc.Node = null

    public model: CarriageModel = null
    protected trainMgr: TrainModel = null
    public builds: cc.Node[] = [] //当前场景的设施
    private passengers: cc.Node[] = []
    private birds: cc.Node[] = []
    private clouds: cc.Node[] = []
    private topPassengers: cc.Node[] = []
    private wastes: cc.Node[] = []
    private fires: cc.Node[] = []

    public listenEventMaps() {
        return [
            { [EventType.UNLOCK_BUILD]: this.onUnlockBuild, tag: 'create' },
            { [EventType.CHANGE_BUILD]: this.onChangeBuild, tag: 'create' },
            { [EventType.PASSENGER_ENTER_CARRIAGE]: this.onRoleEnter, tag: 'create' },
            { [EventType.PASSENGER_EXIT_CARRIAGE]: this.onRoleExit, tag: 'create' },
            { [EventType.SHOW_CARRIAGE_DEBUG_MAP]: this.showCarriageDebugMap },
            { [EventType.ROLE_DROP_MONEY]: this.onRoleDropMoney },
            { [EventType.UPDATE_ALL_DROP]: this.onInitDropMoney },
            { [EventType.ADD_CLOUD]: this.addCloud, tag: 'create' },
            { [EventType.REMOVE_CLOUD]: this.removeCloud, tag: 'create' },

            { [EventType.PASSENGER_ENTER_CARRIAGE_TOP]: this.onRoleEnterTop, tag: 'create' },
            { [EventType.PASSENGER_EXIT_CARRIAGE_TOP]: this.onRoleExitTop, tag: 'create' },

        ]
    }

    init(carriageModel: CarriageModel) {
        if (carriageModel == null) return;
        this.model = carriageModel
        this.initView()
    }

    private showCarriageDebugMap(show: boolean = true) {
        if (show) {
            this.showDebugMap()
        }
        else {
            this.hideDebugMap()
        }
    }

    initView() {
        this.refreshBuilds()
        this.initRoles()
        this.initClouds()
        this.initBurst()
        this.initBirds()
        dropItemHelper.initLightAreas(this.model, this.facility).then(() => {
            dropItemHelper.initDropMoney(this.model, this.facility)
        })
    }

    protected initBuilds() {
        let builds = this.model.getBuilds()
        for (let build of builds) {
            this.createBuild(build, build.isBuilding ? BuildFromType.BUY : BuildFromType.NONE).then((buildNode) => {
                if (!cc.isValid(this) || !buildNode) return
                this.builds.push(buildNode);
            })
        }
    }

    private initRoles() {
        let passengers = this.model.getPassengers()
        for (let passenger of passengers) {
            this.addRole(passenger)
        }

        let topPassengers = this.model.getTopPassengers()
        for (let passenger of topPassengers) {
            this.addRoleTop(passenger)
        }
    }

    private initClouds() {
        if (!(this.model instanceof WaterModel)) return
        let clouds = this.model.clouds
        for (let cloud of clouds) {
            this.addCloud(cloud)
        }
    }

    private onRoleEnter(id: number, passenger: PassengerModel) {
        if (this.model?.getID() != id) return
        this.addRole(passenger)
    }

    private async onRoleExit(id, passenger: PassengerModel) {
        if (this.model?.getID() != id) return
        this.removeRole(passenger)
    }

    private onRoleEnterTop(id: number, passenger: PassengerModel) {
        if (this.model?.getID() != id) return
        this.addRoleTop(passenger)
    }

    private async onRoleExitTop(id, passenger: PassengerModel) {
        if (this.model?.getID() != id) return
        this.removeRoleTop(passenger)
    }

    private addRoleTop(passenger: PassengerModel) {
        let prefab = assetsMgr.getPrefab('role');
        let roleNode = cc.instantiate2(prefab, this.top)
        roleNode.Component(PassengerView).init(passenger)
        this.topPassengers.push(roleNode)
        return roleNode
    }

    private removeRoleTop(passenger: PassengerModel) {
        for (let i = this.topPassengers.length - 1; i >= 0; i--) {
            let node = this.topPassengers[i]
            if (!cc.isValid(node)) { //如果节点已经destroy
                this.topPassengers.splice(i, 1)
                continue
            }
            if (node.Component(PassengerView)['model'] == passenger) {
                this.topPassengers.splice(i, 1)
                node.parent = null
                node.destroy()
                break
            }
        }
    }

    private async addCloud(cloud: WaterCloudObj) {
        if (!(this.model instanceof WaterModel)) return
        let node = await resHelper.loadPrefabByUrl("build/trainItem_1019_1_yun", this.facility, this.getTag())
        if (!cc.isValid(this)) return
        if (!this.model.getCloud(cloud.id)) {
            return
        }
        node.Component(WaterCloudCmpt).init(cloud)
        this.clouds.push(node)
        return node
    }

    private removeCloud(id) {
        if (!(this.model instanceof WaterModel)) return
        for (let i = this.clouds.length - 1; i >= 0; i--) {
            let node = this.clouds[i]
            if (!cc.isValid(node)) { //如果节点已经destroy
                this.clouds.splice(i, 1)
                continue
            }
            if (node.Component(WaterCloudCmpt)['model']?.id == id) {
                this.clouds.splice(i, 1)
                node.parent = null
                node.destroy()
                break
            }
        }
    }

    private initBirds() {
        if (this.model.getID() != 1013) return
        const skins = ["fen_1", "fen_2", "fen_3", "huang_1"]
        const prefab = assetsMgr.getPrefab('dorm_bird');
        for (const skin of skins) {
            const birdNode = cc.instantiate2(prefab, this.facility)
            birdNode.Component(DormBirdView).init(DormBirdModel.create(skin))
            birdNode.zIndex = MAX_ZINDEX + 1
            this.birds.push(birdNode)
            break
        }
    }

    private addRole(passenger: PassengerModel) {
        let prefab = assetsMgr.getPrefab('role');
        let roleNode = cc.instantiate2(prefab, this.facility)
        if (!this.facility) {
            console.error("addRole", roleNode, this.facility)
        }
        roleNode.Component(PassengerView).init(passenger)
        this.passengers.push(roleNode)
        return roleNode
    }

    private removeRole(passenger: PassengerModel) {
        for (let i = this.passengers.length - 1; i >= 0; i--) {
            let node = this.passengers[i]
            if (!cc.isValid(node)) { //如果节点已经destroy
                this.passengers.splice(i, 1)
                continue
            }
            if (node.Component(PassengerView)['model'] == passenger) {
                this.passengers.splice(i, 1)
                node.parent = null
                node.destroy()
                break
            }
        }
    }

    // 解锁设施
    protected async onUnlockBuild(build: BuildObj, delay: number = 0) {
        if (this.model == null) return;
        if (build.carriageId != this.model.getID()) return;
        if (delay > 0) await ut.wait(delay, this)
        this.addBuild(build, BuildFromType.BUY)
        this.drawGrid()
    }

    // 切换设施皮肤
    protected async onChangeBuild(build: BuildObj, first: boolean) {
        if (this.model == null) return;
        if (build.carriageId != this.model.getID()) return;
        this.addBuild(build, first ? BuildFromType.BUY : BuildFromType.CHANGE)
        this.drawGrid()
    }

    //切换主题
    public refreshBuilds() {
        this.builds.forEach(build => {
            build.destroy()
        })
        this.builds = []
        this.initBuilds()
        this.drawGrid()
    }

    private async drawGrid() {
        if (!this.myGrid) return
        await ut.waitNextFrame(2, this)
        this.myGrid.initCarriage(this.model.getMap())
    }

    // 精准的只删跟传参有关的(删全部会出现创建多个时,会影响其它设施的建造动画)
    public onBuildChangeSkin(buildObj: BuildObj) {
        for (let i = this.builds.length - 1; i >= 0; i--) {
            let build = this.builds[i]
            let cmpt = build.Component(BuildCmpt)
            if (!cmpt) continue
            let model = build.Data
            // 前一个风格的此序号的设施  || 默认墙纸/地板
            if ((buildObj.order == model.order && !cmpt.isActive()) || (buildObj.buildType && buildObj.buildType == model.buildType && buildObj != model)) {
                build.walk(n => {
                    let cmpt = n.getComponent(PassengerView)
                    if (!cmpt) return
                    cmpt.resetParent()
                }, () => { })
                build.destroy()
                this.builds.splice(i, 1)
                break
            }
        }
    }

    public async createBuild(build: BuildObj, from: BuildFromType = BuildFromType.NONE) {
        let windTag = mc.currWind.getTag()
        let node = await resHelper.loadBuildPrefab(build.id, this.facility, this.getTag())
        if (!cc.isValid(this)) return

        const url = `build/${build.prefab}`
        assetsMgr.releaseTempRes(url, windTag) //清掉预加载时的引用

        if (cc.isValid(node)) {
            node.setPosition(build.position)
            node.zIndex = build.getZIndex();
            node.Data = build

            let cmpt = node.Component(BuildCmpt)
            if (!cmpt) {
                cmpt = node.addComponent(BuildCmpt)
            }
            cmpt.init(build, this, from)
        }
        return node
    }

    protected async addBuild(data: BuildObj, from: BuildFromType) {
        const it = await this.createBuild(data, from)
        if (!cc.isValid(it)) return
        this.builds.push(it);
    }

    //显示地图占用
    @ut.addLock
    public async showDebugMap() {
        let root = this.facility
        let mapNode = root.Child('map_grid')
        if (!mapNode) {
            let prefab = await assetsMgr.loadTempRes("editor/map_grid", cc.Prefab, this.getTag())
            mapNode = cc.instantiate2(prefab, root)
        }

        let map = this.model.getMap()
        mapNode.active = true
        mapNode.Component('MapGridView').init(map)
        mapNode.setPosition(cc.v2())
        mapNode.zIndex = MAX_ZINDEX * 2
    }

    public hideDebugMap() {
        let mapNode = this.facility.Child('map_grid')
        mapNode.active = false
    }


    // 掉落钱
    private onRoleDropMoney(role: any, drop: DropItemObj, isMerge: boolean) {
        if (gameHelper.world.isOutputTimeFull) return

        if (this.model === role.map) {
            dropItemHelper.playDropMoney(role, drop, this.facility, isMerge)
        }
    }

    private onInitDropMoney(map: CarriageModel) {
        if (this.model === map) {
            dropItemHelper.initDropMoney(this.model, this.facility)
        }
    }

    private initBurst() {
        this.initWastes()
        this.initFires()
    }

    private initWastes() {
        for (let waste of this.model.getWastes()) {
            this.addWaste(waste)
        }
    }

    private initFires() {
        for (let fire of this.model.getFires()) {
            this.addFire(fire)
        }
    }

    private async addWaste(waste: BurstItem) {
        let node = await resHelper.loadPrefabByUrl("burst/waste", this.facility, this.getTag())
        if (!cc.isValid(this)) return
        if (!this.model.getWastes().has(waste)) {
            return
        }
        node.Data = waste
        node.zIndex = waste.getZIndex()
        node.setPosition(waste.pos)
        this.wastes.push(node)
        return node
    }

    private async addFire(fire: BurstItem) {
        let node = await resHelper.loadPrefabByUrl("burst/fire", this.facility, this.getTag())
        if (!cc.isValid(this)) return
        if (!this.model.getFires().has(fire)) {
            return
        }
        node.Data = fire
        node.scale = fire.scale
        node.zIndex = fire.getZIndex()
        node.setPosition(fire.pos)
        this.fires.push(node)
        return node
    }

    private removeWaste(waste: BurstItem) {
        return this.removeItem(this.wastes, waste)
    }

    private removeFire(fire: BurstItem) {
        return this.removeItem(this.fires, fire)
    }

    private removeItem(nodes: cc.Node[], item: any, check?: Function) {
        for (let i = nodes.length - 1; i >= 0; i--) {
            let node = nodes[i]
            if (!cc.isValid(node)) { //如果节点已经destroy
                nodes.splice(i, 1)
                continue
            }
            if (check ? check(node.Data) : node.Data == item) {
                nodes.splice(i, 1)
                node.parent = null
                node.destroy()
                break
            }
        }
    }
}
