import { CarriageID, CarriageType, CarriageUsePosType, GoodsType, GuideModule, PassengerLifeAnimation, PassengerLifeEvent, RoleDir, TrainBurstTaskType, TrainDailyTaskType } from "../../../common/constant/Enums"
import EventType from "../../../common/event/EventType"
import { gameHelper } from "../../../common/helper/GameHelper"
import ActionTree, { ActionNode } from "../ActionTree"
import PassengerModel from "../PassengerModel"
import { TimeStateData } from "../StateDataType"
import { StateType } from "../StateEnum"
import CarriageModel from "../../train/common/CarriageModel"
import { GoGameCfg } from "../../../common/constant/DataType"
import { CARRIAGE_CONTENT_SIZE, CARRIAGE_LEN, CARRIAGE_SPACING, MAX_VALUE, RoleJump, THROW_FUEL_SPEED } from "../../../common/constant/Constant"
import { ActAnimCfg, ActCondType, ActionCfg, ActType, CARRIAGE_MAX_STAY_TIME, PARTORL_CARRAIGE_TIME, RoleActCfg } from "./ActionCfg"
import BuildObj from "../../train/common/BuildObj"
import { unlockHelper } from "../../../common/helper/UnlockHelper"
import DiningModel from "../../train/dining/DiningModel"
import { Shape } from "../../map/MoveModel"

export default class BaseAction {
    protected role: PassengerModel
    protected actionTree: ActionTree
    public data: any = {}

    protected waitRecord: any = null

    protected carriage: CarriageModel = null

    protected get actionAgent() {
        return this.role?.actionAgent
    }

    protected get moveAgent() {
        return this.role?.moveAgent
    }

    debug(...params) {
        // let id = this.role?.id
        // console.log(`[${id}]:[${this.carriage.getID()}] `, ...params)
    }

    debugE(...params) {
        // let id = this.role?.id
        // console.error(`[${id}]:[${this.carriage.getID()}] `, ...params)
    }

    public init(role: PassengerModel) {
        this.role = role
        this.actionTree = new ActionTree().init(this)

        this.carriage = this.role.carriage

        this.actionTree.start(this.lifeCycle)
        return this
    }

    protected async lifeCycle(action) {
        await action.run(this.onLoad)
        if (this.role.getTrainDailyTask()) {
            await action.run(this.handleTrainDailyTask)
        }
        if (this.role.getTrainBurstTask()) {
            await action.run(this.handleTrainBurstTask)
        }
        await action.run(this.start)
        await action.run(this.onExit)
        action.ok()
    }

    protected async onLoad(action) {
        let actionAgent = this.actionAgent
        let fromCarriage = actionAgent.fromCarriage
        if (fromCarriage) { //从其他车厢过来的
            await action.run(this.enterCarriage, this.actionAgent.fromCarriage)
            actionAgent.fromCarriage = null

            if (fromCarriage.getType() != CarriageType.DORM) {
                this.actionAgent.addCarriageCD(fromCarriage)
            }

            await action.run(this.handleWants)
        }
        else { //正常初始化/从被背包进来的
            if (actionAgent.enterFromTop) {
                actionAgent.enterFromTop = false
                await action.run(this.toCheckIn)
            }
            if (actionAgent.exitToTop) {
                actionAgent.exitToTop = false
                await action.run(this.exitToTop)
            }
        }

        action.ok()
    }

    protected async start(action: ActionNode) {
        this.debug('start')
        await action.run(this.idle)
        await action.run(this.toRandomPos)
    }

    protected async onBeforeStart(action: ActionNode) {
        if (this.role.isCheckIn()) {
            await this.handleGather(action)
            if (action.isOK()) return
            if (!this.checkSleep()) {
                return
            }
            // 该睡觉了 取消聚集
            this.role?.actionAgent?.clearTrainActivityMoveTarget()
            await this.handleEvents(action)
        }
    }

    protected async onExit(action) {
        action.ok()
    }

    protected async handleGather(action: ActionNode) {
        let targetCarriageId = this.role?.actionAgent?.trainActivityMoveTarget
        let targetCarriage = gameHelper.train.getCarriageById(targetCarriageId)
        let carriageId = this.role?.carriageId
        // let isInSide = (!!this.role.dorm && this.role.dorm.getID() == targetCarriageId) || (!!this.role.workCarrige && this.role.workCarrige.getID() == targetCarriageId)
        if (!!targetCarriageId) {
            if (targetCarriageId != carriageId) {
                await action.run(this.moveToCarriage, targetCarriage)
                action.ok()
            }
            else {
                // 随机移动
                await action.run(this.toRandomPos)
            }
        }
    }

    protected async handleEvents(action: ActionNode) {
        let hasWork = this.role.hasWork()
        if (!hasWork && this.checkDinner()) {
            this.actionAgent.pushState(StateType.WANT_EAT)
            await action.run(this.toDiningRoom)
            action.ok()
        }
        else if (this.checkSleep()) {
            await this.handleSleep(action)
        }
        else if (this.checkLeave()) {
            let carriage
            if (hasWork) {
                carriage = this.role.workCarrige
            }
            if (carriage == this.role.carriage) return
            await action.run(this.toChangeCarriage, carriage)
            action.ok()
        }
        else if (!hasWork && this.checkGoHome()) {
            await action.run(this.goHome)
            action.ok()
        }
    }

    protected async handleSleep(action: ActionNode) {
        let carriage = this.role.carriage
        let dorm = this.role.dorm
        if (!dorm) { //工作乘客
            await action.run(this.toTopSleep)
            return action.ok()
        }
        if (carriage == dorm) {
            await action.run(this.toSleep)
        }
        else {
            // console.log("goHome", carriage.getID(), dorm.getID())
            this.actionAgent.pushState(StateType.WANT_SLEEP)
            await action.run(this.goHome)
            action.ok()
        }
    }

    // 走出车厢然后进车顶
    protected async exitToTop(action: ActionNode) {
        await action.run(this.exitCarriage, this.carriage)
        this.role.putToTop()
        action.ok()
    }
    protected async toTopSleep(action: ActionNode) {
        await action.run(this.exitCarriage, this.carriage)
        this.role.awakeWorkTime = this.getAwakeTime()
        this.role.putToTop()
        action.ok()
    }

    protected async handleWants(action) {
        if (this.actionAgent.getState(StateType.WANT_SLEEP)) {
            return this.handleWantSleep(action)
        }
        action.ok()
    }

    protected async handleWantSleep(action) {
        let type = StateType.WANT_SLEEP
        this.actionAgent.popState(type)
        let event1 = this.role.getEvent(PassengerLifeEvent.SLEEP)
        if (!event1 || !this.isSleepTime()) return action.ok()  //过时间了就不睡了
        await action.run(this.toSleep)
        action.ok()
    }

    protected async toSleep(action) {
        await action.run(this.sleep, { anim: PassengerLifeAnimation.STAND_SLEEP })
        action.ok()
    }

    //坐下后延迟行动的时间
    protected getSitDelayTime() {
        return ut.randomRange(1, 2)
    }

    protected checkSleep() {
        if (!unlockHelper.useRealTime()) return
        if (!this.isSleepTime()) return
        return true
    }

    protected isSleepTime() {
        let type1 = PassengerLifeEvent.SLEEP, type2 = PassengerLifeEvent.WAKEUP
        let event1 = this.role.getEvent(type1)
        if (!event1) return false
        let dayTime = gameHelper.world.getDayTime()
        let eventInfo1 = this.role.getLastEventInfo(type1, dayTime)
        let eventInfo2 = this.role.getLastEventInfo(type2, dayTime)
        let res = false
        if (eventInfo1.time > dayTime && eventInfo2.time < dayTime) {
            res = false
        }
        else if (eventInfo2.time > dayTime && eventInfo1.time < dayTime) {
            res = true
        }
        else {
            res = eventInfo1.time > eventInfo2.time
        }
        // console.log("checkSleep", ut.millisecondFormat(eventInfo1.time, "hh:mm"), ut.millisecondFormat(eventInfo2.time, "hh:mm"), res)
        return res
    }

    protected checkEvent(type1, type2?) {
        let event1 = this.role.getEvent(type1)
        if (!event1) return false
        let dayTime = gameHelper.world.getDayTime()
        let passDay = gameHelper.world.getPassDay()
        let lastCheckEventTime = this.actionAgent.lastCheckEventTime[type1]
        let eventInfo1 = this.role.getLastEventInfo(type1, dayTime)
        let nowEventTime = passDay + eventInfo1.time
        this.actionAgent.lastCheckEventTime[type1] = nowEventTime
        if (lastCheckEventTime == nowEventTime) { //先判断在不在时间段内
            // twlog.info("checkEvent fail", type1, ut.millisecondFormat(lastCheckEventTime % ut.Time.Day, "hh:mm", ), ut.millisecondFormat(eventInfo1.time, "hh:mm"), ut.millisecondFormat(dayTime, "hh:mm"))
            return false
        }
        let succ = ut.chance(eventInfo1.pro)
        if (!succ) {
            // twlog.info("checkEvent fail3", type1, eventInfo1.pro)
        }
        else {
            // twlog.info("checkEvent succ", type1)
        }

        // if (succ && type2) { //如果成功，则不校验未处理的互斥事件
        //     let eventInfo2 = this.role.getLastEventInfo(type2)
        //     lastCheckEventTime = this.actionAgent.lastCheckEventTime[type2]
        //     nowEventTime = passDay + eventInfo2.time
        //     this.actionAgent.lastCheckEventTime[type2] = nowEventTime
        // }

        return succ
    }

    protected getAwakeTime() {
        if (!this.isSleepTime()) return 0

        let now = gameHelper.world.getTime()
        let dayTime = gameHelper.world.getDayTime()
        let wakeUpEvent = this.role.getEvent(PassengerLifeEvent.WAKEUP)
        let wakeUpInfo = this.role.getNextEventInfo(PassengerLifeEvent.WAKEUP, dayTime)
        let randomTime = this.randomCount(wakeUpEvent.timeRange)
        if (wakeUpInfo.time < dayTime) {
            wakeUpInfo.time += ut.Time.Day
        }
        let awakeTime = now + (wakeUpInfo.time - dayTime) + randomTime * ut.Time.Minute
        return awakeTime
    }

    protected getJumpInfo(dap: boolean = true) {
        let time = 0, jumpTime = 0
        if (this.canJump()) { //没有跳跃动画直接闪现上去
            jumpTime = 0.3 + 0.3 //蓄力 + 跳跃时间
            time = jumpTime + this.actionAgent.getAnimsTime([
                PassengerLifeAnimation.STAND_TO_SIT,
            ])
            if (dap) {
                time += this.actionAgent.getAnimTime(PassengerLifeAnimation.SIT_DAP)
            }
        }
        return { time, jumpTime }
    }

    protected getDownInfo() {
        let time = 0, downTime = 0
        if (this.canJump()) {
            downTime = 0.3 + 0.3 //蓄力 + 落下时间
            time = this.actionAgent.getAnimsTime([
                PassengerLifeAnimation.SIT_TO_STAND,
            ]) + downTime
        }
        return { time, downTime }
    }

    protected getActAnims(prefix) {
        let anims = []
        for (let i = 1; i < 5; i++) {
            let name = prefix
            if (i > 1) {
                name += i
            }
            let anim = this.actionAgent.getAnim(name)
            if (anim) {
                anims.push(Object.assign({ name }, anim))
            }
            else {
                break
            }
        }
        return anims
    }

    protected getActCfg(condType) {
        let cfgs: any[] = RoleActCfg[this.role.getID()]
        if (cfgs) {
            cfgs = cfgs.filter(c => !c.condType || c.condType == condType && c.random !== false)
            if (cfgs.length <= 0) return
            let index = gameHelper.randomByWeight(cfgs)
            return cfgs[index]
        }
    }

    protected getTimeByCfg(type, name: string) {
        let data = ActionCfg[type]
        if (!data) {
            twlog.error("getTimeByCfg error", type)
            return 1
        }
        return this.randomLoopAnimTime(data, name)
    }

    protected getActAnimTime(name) { //循环播的
        for (let type in ActAnimCfg) {
            let data = ActAnimCfg[type]
            if (data.id == this.role.id && data.anim == name) {
                return this.getTimeByCfg(type, name)
            }
        }
        return 0
    }

    //在完整播完一次的前提下，随机一个播放时长
    protected randomLoopAnimTime(data, name: string) {
        let animTime = this.actionAgent.getAnimTime(name)
        let time = this.randomTime(data)
        return Math.ceil(time / animTime) * animTime //要完整播完
    }

    protected checkBuildPlay(build, useIndexes: (number | string)[] = [0]) {
        if (!build) return false
        return useIndexes.some(i => {
            let index = i as number
            if (typeof i == 'string') {
                index = build.getUseIndexById(i)
            }
            if (!build.canUse(index)) return false
            if (this.actionAgent?.isCdBuildRecord(build, index)) return false
            return true
        })
    }

    protected checkBuildQueue(build: BuildObj, checkCD: boolean = true) {
        if (!build) return false
        if (!build.canQueue()) return false
        if (checkCD && this.actionAgent.isCdBuildRecord(build, 0)) return false
        return true
    }

    protected getUseBuild(builds: BuildObj[], isPlay: boolean, useIndexes?: number[]) {
        return builds.filter(build => {
            if (isPlay) {
                return this.checkBuildPlay(build, useIndexes)
            }
            else {
                return build && build.canUse()
            }
        }).random()
    }

    protected randomUseBuild(ary: { build: BuildObj, index: number }[], isPlay: boolean) {
        return ary.filter(({ build, index }) => {
            if (isPlay) {
                return this.checkBuildPlay(build, [index])
            } else {
                return build.canUse(index)
            }
        }).random()
    }

    protected canJump() {
        return !!this.actionAgent.getAnim(PassengerLifeAnimation.JUMP)
    }

    protected checkStandAct() {
        return !!this.actionAgent.getAnim(PassengerLifeAnimation.STAND_ACT)
    }

    protected checkSitAct() {
        return !!this.actionAgent.getAnim(PassengerLifeAnimation.SIT_ACT)
    }

    protected checkSitDrink() {
        return !!this.actionAgent.getAnim(PassengerLifeAnimation.SIT_DRINK)
    }

    protected checkSitEat() {
        return !!this.actionAgent.getAnim(PassengerLifeAnimation.SIT_EAT)
    }

    protected async runRandomAct(action, cfgs, params?) {
        cfgs = cfgs.filter(({ check }) => {
            if (check) return check.call(this)
            return true
        })
        if (cfgs.length > 0) {
            let rdIndex = gameHelper.randomByWeight(cfgs)
            let cfg = cfgs[rdIndex]
            await action.run(cfg.act, Object.assign({}, params, cfg.params))
            return true
        }
        return false
    }

    protected randomCount(section) {
        if (section.length == 2 && cc.js.isNumber(section[0])) {
            return ut.random(section[0], section[1])
        }
        else {
            let index = gameHelper.randomByWeight(section)
            return section[index].num
        }
    }

    protected randomTime(section) {
        return ut.randomRange(section[0], section[1])
    }

    //--------------- check in ----------------
    protected async toCheckIn(action) {
        let actionAgent = this.actionAgent
        let state = StateType.CHECK_IN
        actionAgent.pushState(state)

        let carriage = this.carriage
        let posList = carriage.getUsePosListByType(CarriageUsePosType.CHECK_IN)
        let count = Math.floor(posList.length / 2)
        let indexes = ut.newArray(count).map((_, i) => i)
        indexes = ut.randomArray(indexes)
        let index = indexes[0]
        for (let i of indexes) {
            let startInfo = posList[i * 2]
            if (carriage.checkUsePos(startInfo.index)) {
                index = i
                break
            }
        }

        let startInfo = posList[index * 2], endInfo = posList[index * 2 + 1]
        if (startInfo) {
            this.role.setPosition(startInfo.pos)
            this.role.setDir(startInfo.dir)
            carriage.setUseLock(true, startInfo.index) //这里可能会有多次引用，不过应该没问题
        }

        action.onTerminate = () => {
            carriage.setUseLock(false, startInfo.index)
            actionAgent.popState(state)
        }
        actionAgent.setIdleAnim(state, PassengerLifeAnimation.CHECK_IN_IDLE)
        actionAgent.setMoveAnim(state, PassengerLifeAnimation.CHECK_IN_WALK)
        await action.run(this.idle, { time: ut.randomRange(0.5, 2.5) })
        if (endInfo) {
            await action.run(this.moveByPath, { paths: [endInfo] })
        }
        else {
            await action.run(this.toRandomPos)
        }
        await action.run(this.checkInTidy)
        action.onTerminate()
        action.ok()
    }

    protected async checkInTidy(action) {
        this.debug("checkInTidy")
        let type = StateType.CHECK_IN_TIDY
        let time = this.actionAgent.getAnimsTime([
            PassengerLifeAnimation.CHECK_IN_TIDY,
        ])
        let timeData = new TimeStateData().init(time)
        action.onTerminate = () => {
            this.actionAgent.popState(type)
        }
        this.actionAgent.pushState(type, { timeData, anim: PassengerLifeAnimation.CHECK_IN_TIDY, loop: false })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    //-------------------------------

    //-------------列车每日任务------------------
    protected async handleTrainDailyTask(action: ActionNode) {
        let role = this.role
        let task = role.getTrainDailyTask()
        if (!task) return action.ok()
        let type = task.id
        switch (type) {
            case TrainDailyTaskType.PATROL:
                await action.run(this.handleTrainDailyTaskPatrol)
                break
            case TrainDailyTaskType.REPAIR:
                await action.run(this.handleTrainDailyTaskRepair)
                break
            case TrainDailyTaskType.OVERTIME:
                await action.run(this.handleTrainDailyTaskOvertime)
                break
            case TrainDailyTaskType.CLEAN:
                await action.run(this.handleTrainDailyTaskSweep)
                break
            case TrainDailyTaskType.STAND:
                await action.run(this.handleTrainDailyTaskStand)
                break
            case TrainDailyTaskType.LOST_SEARCH:
                await action.run(this.handleTrainDailyTaskLostSearch)
            case TrainDailyTaskType.INSPECT:
                await action.run(this.handleTrainDailyTaskInspect)
                break
            case TrainDailyTaskType.CLOTHES_CLEAN:
                await action.run(this.handleTrainDailyTaskClothesClean)
            case TrainDailyTaskType.SAFETY_PROMOTION:
                await action.run(this.handleTrainDailyTaskSafetyPromotion)
                break
            case TrainDailyTaskType.LIFE_GOODS:
                await action.run(this.handleTrainDailyTaskLifeGoods)
                break
            default:
                action.ok()
        }
        action.ok()
    }

    protected async handleTrainDailyTaskPatrol(action) {
        let type = StateType.PATROL
        let actionAgent = this.actionAgent
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        actionAgent.pushState(type)
        this.actionAgent.setIdleAnim(type, PassengerLifeAnimation.CHECK_IN_IDLE)
        this.actionAgent.setMoveAnim(type, PassengerLifeAnimation.WALK_JOB)

        let act = async (action) => {
            await action.run(this.toRandomPos)
            await action.run(this.idle)
        }

        let flow = async (action: ActionNode) => {
            await action.race([
                [act],
                [ActionTree.wait, this.randomTime(PARTORL_CARRAIGE_TIME)]
            ])
            await action.run(this.toChangeCarriage)
        }
        await action.run(flow)
    }

    protected async handleTrainDailyTaskRepair(action) {
        let type = StateType.ON_REPAIR
        let actionAgent = this.actionAgent
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        actionAgent.pushState(type)
        let role = this.role
        let carriage = this.carriage

        this.actionAgent.setIdleAnim(type, PassengerLifeAnimation.IDLE_JOB)
        this.actionAgent.setMoveAnim(type, PassengerLifeAnimation.WALK_JOB)

        let checkRepair = () => {
            if (actionAgent.getAnim(PassengerLifeAnimation.REPAIR)) {
                return this.carriage.checkUsePosByType(CarriageUsePosType.REPAIR)
            }
            return false
        }

        let toRepair = async (action) => {
            let posList = carriage.getUsePosListByType(CarriageUsePosType.REPAIR)
            let info = posList.filter(info => carriage.checkUsePos(info.index)).random()
            let pos = info.pos
            action.onTerminate = () => {
                carriage.setUseLock(false, info.index)
            }
            carriage.setUseLock(true, info.index)
            await action.run(this.move, pos)
            role.setDir(info.dir)
            await action.run(repair)
            action.onTerminate()
            action.ok()
        }

        let repair = async (action) => {
            let type = StateType.REPAIR
            let anim = PassengerLifeAnimation.REPAIR
            action.onTerminate = () => {
                this.actionAgent.popState(type)
            }
            let time = this.getTimeByCfg(type, anim)
            let timeData = new TimeStateData().init(time)
            this.actionAgent.pushState(type, { timeData, anim })
            await action.wait(timeData)
            action.onTerminate()
            action.ok()
        }

        let flow = async (action) => {
            if (checkRepair()) {
                await action.run(toRepair)
            }
            else {
                await action.run(this.toRandomPos)
            }
        }
        await action.run(flow)
    }

    protected async handleTrainDailyTaskOvertime(action) {
        let type = StateType.OVERTIME
        let actionAgent = this.actionAgent
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        actionAgent.pushState(type, { anim: PassengerLifeAnimation.WORK })
        await action.wait(-1)
    }

    protected async handleTrainDailyTaskSweep(action) {
        let type = StateType.ON_CLEAN
        let actionAgent = this.actionAgent
        let moveAgent = this.moveAgent
        action.onTerminate = () => {
            actionAgent.popState(type)
            moveAgent.clearShape()
        }

        let sweep
        let cleanType
        let idleAnim, moveAnim
        if (actionAgent.getAnim(PassengerLifeAnimation.CLEAN)) {
            sweep = this.toSweepBuild
            cleanType = CarriageUsePosType.CLEAN
        }
        else {
            moveAnim = `${PassengerLifeAnimation.CLEAN}2_walk`
            idleAnim = PassengerLifeAnimation.CHECK_IN_IDLE
            sweep = this.toSweepFloor
            cleanType = CarriageUsePosType.CLEAN_FLOOR
            moveAgent.setShape({ size: cc.size(12 + 1, 1) })
        }
        this.actionAgent.setIdleAnim(type, idleAnim)
        this.actionAgent.setMoveAnim(type, moveAnim)

        actionAgent.pushState(type, { cleanType })

        let flow = async (action) => {
            await action.run(sweep)
        }
        await action.run(flow)
    }

    protected async handleTrainDailyTaskStand(action) {
        let stateType = StateType.STAND
        action.onTerminate = () => {
            this.actionAgent.popState(stateType)
        }

        let pos
        let dir = RoleDir.RIGHT
        let carriage = this.role.carriage
        let type = 0
        if (carriage.getID() == CarriageID.HEAD) {
            let info = carriage.getUsePosListByType(CarriageUsePosType.STAND).random()
            pos = info.pos
        }
        else {
            pos = cc.v2(1879, -5)
            type = 1
        }
        this.role.setPosition(pos)
        this.role.setDir(dir)

        this.actionAgent.pushState(stateType, { type })

        let sleep = async (action) => {
            await action.run(this.sleep, { time: this.randomTime([10, 30]), anim: PassengerLifeAnimation.STAND_SLEEP })
            await action.run(this.idle, { time: this.randomTime([30, 60]) })
            action.ok()
        }
        let cfgs = [
            { act: sleep, weight: 10 }, //睡觉
            { act: this.idle, weight: 90 },
        ]
        let flow = async (action) => {
            await this.runRandomAct(action, cfgs)
        }
        await action.run(flow)
    }

    protected async handleTrainDailyTaskLostSearch(action: ActionNode) {
        let type = StateType.LOST_SEARCH
        let actionAgent = this.actionAgent
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        actionAgent.pushState(type)
        actionAgent.setIdleAnim(type, PassengerLifeAnimation.SEARCH)
        actionAgent.setMoveAnim(type, PassengerLifeAnimation.SEARCH_WALK)

        let search = async (action) => {
            await action.run(this.toRandomPos)
            await action.run(this.idle)
        }

        let flow = async (action: ActionNode) => {
            await action.race([
                [search],
                [ActionTree.wait, this.randomTime(PARTORL_CARRAIGE_TIME)]
            ])
            await action.run(this.toChangeCarriage)
        }
        await action.run(flow)
    }

    protected async handleTrainDailyTaskInspect(action) {
        let type = StateType.INSPECT
        let actionAgent = this.actionAgent
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        actionAgent.pushState(type)
        actionAgent.setIdleAnim(type, PassengerLifeAnimation.SEARCH)
        actionAgent.setMoveAnim(type, PassengerLifeAnimation.SEARCH_WALK)

        let flow = async (action: ActionNode) => {
            await action.run(this.toRandomPos)
            await action.run(this.idle)
        }
        await action.run(flow)
    }

    protected async handleTrainDailyTaskClothesClean(action) {
        let type = StateType.CLOTHES_CLEAN
        let actionAgent = this.actionAgent
        this.role.setDir(ut.chance(50) ? RoleDir.RIGHT : RoleDir.LEFT)
        let size = cc.size(18, 4)
        let shape = new Shape(size, cc.v2(5, size.height - 1))
        let map = this.carriage.getMap()
        this.moveAgent.setShape(shape)

        let pos = this.role.getRandomMovePos([])
        this.role.setPosition(pos)

        let point = map.getActPointByPixel(pos)
        let points = shape.getPoints(point.x, point.y, this.role.getDir())
        map.updatePointsByPoints(points, true)

        action.onTerminate = () => {
            actionAgent.popState(type)
            map.updatePointsByPoints(points, false)
            this.moveAgent.clearShape()
        }
        actionAgent.pushState(type, { anim: PassengerLifeAnimation.WASH })
        await action.wait(-1)
    }

    protected async handleTrainDailyTaskSafetyPromotion(action) {
        let type = StateType.SAFETY_PROMOTION
        let actionAgent = this.actionAgent
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        actionAgent.pushState(type)
        actionAgent.setIdleAnim(type, PassengerLifeAnimation.CHECK_IN_IDLE)
        actionAgent.setMoveAnim(type, PassengerLifeAnimation.CHECK_IN_WALK)

        let search = async (action) => {
            await action.run(this.toRandomPos)
            await action.run(this.idle)
        }

        let flow = async (action: ActionNode) => {
            await action.race([
                [search],
                [ActionTree.wait, this.randomTime(PARTORL_CARRAIGE_TIME)]
            ])
            await action.run(this.toChangeCarriage)
        }
        await action.run(flow)
    }

    protected async handleTrainDailyTaskLifeGoods(action) {
        let type = StateType.LIFE_GOODS
        let actionAgent = this.actionAgent
        let moveAgent = this.moveAgent
        action.onTerminate = () => {
            actionAgent.popState(type)
            moveAgent.clearShape()
        }
        actionAgent.pushState(type)
        actionAgent.setIdleAnim(type, PassengerLifeAnimation.CHECK_IN_IDLE)
        actionAgent.setMoveAnim(type, PassengerLifeAnimation.CHECK_IN_WALK)
        moveAgent.setShape({ size: cc.size(18 + 1, 1) })

        let flow = async (action: ActionNode) => {
            await action.run(this.toRandomPos)
            await action.run(this.idle)
        }
        await action.run(flow)
    }
    //-------------------------------


    //-------------- 列车突发事件 --------------------
    protected async handleTrainBurstTask(action) {
        let role = this.role
        let task = role.getTrainBurstTask()
        if (!task) return action.ok()
        let type = task.id
        switch (type) {
            case TrainBurstTaskType.PIRATE:
                await action.run(this.handleTrainBurstTaskPirate)
                break
            case TrainBurstTaskType.CLEAN:
                await action.run(this.handleTrainDailyTaskSweep)
                // await action.run(this.handleTrainBurstTaskClean)
                break
            case TrainBurstTaskType.METEOR:
                await action.run(this.handleTrainBurstTaskMeteor)
                break
            case TrainBurstTaskType.POWER:
                await action.run(this.handleTrainBurstTaskPower)
                break
            case TrainBurstTaskType.FIRE:
                await action.run(this.handleTrainBurstTaskFire)
                break
            case TrainBurstTaskType.WAVE:
                await action.run(this.handleTrainBurstTaskWave)
                break
            default:
                action.ok()
        }
        action.ok()
    }

    protected async handleTrainBurstTaskPirate(action) {
        let role = this.role
        let task = role.getTrainBurstTask()
        if (!task) return action.ok()
        let type = task.id
    }

    protected async handleTrainBurstTaskClean(action) {
        let role = this.role
        let task = role.getTrainBurstTask()
        if (!task) return action.ok()
        let type = task.id
    }

    protected async handleTrainBurstTaskMeteor(action) {
        let type = StateType.ON_REPAIR
        let actionAgent = this.actionAgent
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        actionAgent.pushState(type)
        let role = this.role
        let carriage = this.carriage

        let checkRepair = () => {
            if (actionAgent.getAnim(PassengerLifeAnimation.REPAIR)) {
                return this.carriage.checkUsePosByType(CarriageUsePosType.REPAIR)
            }
            return false
        }

        let toRepair = async (action) => {
            let posList = carriage.getUsePosListByType(CarriageUsePosType.REPAIR)
            let info = posList.filter(info => carriage.checkUsePos(info.index)).random()
            let pos = info.pos
            action.onTerminate = () => {
                carriage.setUseLock(false, info.index)
            }
            carriage.setUseLock(true, info.index)
            await action.run(this.move, pos)
            role.setDir(info.dir)
            await action.run(repair)
            action.onTerminate()
            action.ok()
        }

        let repair = async (action) => {
            let type = StateType.REPAIR
            let anim = PassengerLifeAnimation.REPAIR
            action.onTerminate = () => {
                this.actionAgent.popState(type)
            }
            let time = this.getTimeByCfg(type, anim)
            let timeData = new TimeStateData().init(time)
            this.actionAgent.pushState(type, { timeData, anim })
            await action.wait(timeData)
            action.onTerminate()
            action.ok()
        }

        let flow = async (action) => {
            if (checkRepair()) {
                await action.run(toRepair)
            }
            else {
                await action.run(this.toRandomPos)
            }
        }
        await action.run(flow)
    }

    protected async handleTrainBurstTaskPower(action) {
        let type = StateType.ON_REPAIR
        let actionAgent = this.actionAgent
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        actionAgent.pushState(type)

        let toRepair = async (action) => {
            let pos = this.role.getRandomMovePos()
            await action.run(this.move, pos)
            await action.run(repair)
            action.ok()
        }

        let repair = async (action) => {
            let type = StateType.REPAIR
            let anim = PassengerLifeAnimation.REPAIR
            action.onTerminate = () => {
                this.actionAgent.popState(type)
            }
            let time = this.getTimeByCfg(type, anim)
            let timeData = new TimeStateData().init(time)
            this.actionAgent.pushState(type, { timeData, anim })
            await action.wait(timeData)
            action.onTerminate()
            action.ok()
        }

        let flow = async (action) => {
            await action.run(toRepair)
        }
        await action.run(flow)
    }

    protected async handleTrainBurstTaskFire(action) {
        let role = this.role
        let actionAgent = this.actionAgent

        let addWater = async (action: ActionNode) => {
            this.debug("addFuel")
            let { build } = action.params || {}
            let anim = PassengerLifeAnimation.ENGINE_THROW
            if (!actionAgent.getAnim(anim)) {
                anim = PassengerLifeAnimation.IDLE
            }
            let mountPoint = "shaozi_guadian"
            let fires = role.carriage.getFires().filter(fire => !fire.userId)
            if (fires.length <= 0) {
                fires = role.carriage.getFires()
            }
            let fire = fires.random()
            if (!fire) {
                return action.ok()
            }
            action.onTerminate = () => {
                fire.userId = null
            }
            fire.userId = role.id
            let targetPos = fire.pos
            let curPos = role.getPosition()
            let dis = targetPos.sub(curPos).mag()
            let x = targetPos.x + ut.randomRange(-30, 30)
            let y = targetPos.y + 78
            targetPos = cc.v2(x, y)
            let throwTime = dis / THROW_FUEL_SPEED
            let time = actionAgent.getAnimTime(anim) + throwTime
            let type = StateType.ENGINE_ADD_FUEL
            let timeData = new TimeStateData().init(time)
            action.onTerminate = () => {
                actionAgent.popState(type)
            }
            this.role.setDirToPos(targetPos)
            actionAgent.pushState(type, { build, timeData, anim, mountPoint, targetPos, throwTime, fuelType: 3, nodeName: "water" })
            await action.wait(timeData)
            action.onTerminate()
            action.ok()
        }

        let flow = async (action) => {
            await action.run(addWater)
            await action.wait(ut.randomRange(1, 2))
        }
        await action.run(flow)
    }

    protected async handleTrainBurstTaskWave(action) {
        let role = this.role
        let task = role.getTrainBurstTask()
        if (!task) return action.ok()
        let type = task.id
    }

    //--------------------------------


    //-------------------------------
    protected async toSweepFloor(action) {
        let targetPos = this.role.getRandomMovePos()
        this.moveAgent.searchPathSync(null, targetPos)
        let paths = this.moveAgent.getPaths().slice()
        this.moveAgent.stop()

        let anim = `${PassengerLifeAnimation.CLEAN}2`

        let step = Math.min(5, paths.length)
        if (step <= 0) {
            await action.wait(1)
        } else {
            for (let i = step - 1; i < paths.length; i += step) {
                let pos = paths[i]
                if (i < paths.length - 1) { //最后一个是像素点
                    pos = this.carriage.getMap().getActPixelByPoint(pos)
                }
                await action.run(this.forceMove, pos)
                await action.run(this.sweep, { anim })
            }
        }

        action.ok()
    }

    // 鸡毛掸子
    protected async toSweepBuild(action) {
        let posList = this.carriage.getUsePosListByType(CarriageUsePosType.CLEAN)
        let carriage = this.role.carriage
        let info = posList.filter(info => carriage.checkUsePos(info.index)).random()
        let pos, dir
        if (info) {
            pos = info.pos
            dir = info.dir
            action.onTerminate = () => {
                carriage.setUseLock(false, info.index)
            }
            carriage.setUseLock(true, info.index)
        }
        else {
            action.onTerminate = () => {
            }
            pos = this.role.getRandomMovePos()
        }

        await action.run(this.move, { pos })
        this.role.setDir(dir)
        await action.run(this.sweep, { anim: PassengerLifeAnimation.CLEAN })
        action.onTerminate()
        action.ok()
    }

    protected async sweep(action) {
        let { anim, time } = action.params || {}
        let state = StateType.CLEAN
        anim = anim || PassengerLifeAnimation.IDLE
        action.onTerminate = () => {
            this.actionAgent.popState(state)
        }
        time = time || this.getTimeByCfg(state, anim)
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(state, { timeData, anim, loop: true })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }
    //-------------------------------

    //-------------- 基础行为 --------------------
    protected async idle(action: ActionNode) {
        let type = StateType.IDLE
        let { time } = action.params || {}
        let idleTime = time || this.randomTime(ActionCfg[type])
        this.debug('idle:', idleTime)
        this.actionAgent.pushState(type);
        action.onTerminate = () => {
            this.actionAgent.popState(type)
        }
        await action.wait(idleTime)
        action.onTerminate()
        action.ok()
    }

    protected async sleep(action) {
        this.debug("sleep")
        let { build, time, anim, hideEffect, awakeTime, mountPoint } = action.params
        let actionAgent = this.actionAgent
        let type = StateType.SLEEP
        let data: any = { build, anim, hideEffect, mountPoint }
        let carriage = this.carriage
        let role = this.role
        let now = gameHelper.now()
        action.onTerminate = () => {
            carriage.addAccTotal(role, (gameHelper.now() - now) / ut.Time.Hour)
            actionAgent.popState(StateType.SLEEP)
        }
        if (time) {
            let timeData = new TimeStateData().init(time)
            data.timeData = timeData
            actionAgent.pushState(type, data)
            await action.wait(timeData)
        }
        else {
            if (!awakeTime) {
                awakeTime = this.getAwakeTime()
            }
            data.awakeTime = awakeTime
            actionAgent.pushState(type, data)
            await action.run(this.checkSleepEnd)
        }
        action.onTerminate()
        action.ok()
    }

    protected checkSleepEnd(action) {
        let time = gameHelper.world.getTime()
        let state = this.actionAgent.getState(StateType.SLEEP)
        if (!state || state.data.awakeTime <= time) {
            return action.ok()
        }
    }

    protected async sit(action) {
        this.debug("sit")
        let { build, time, anim, mountPoint } = action.params
        let actionAgent = this.actionAgent
        let type = StateType.SIT
        action.onTerminate = () => {
            actionAgent.popState(type)
        }
        time = time || this.randomTime(ActionCfg[type])
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(type, { build, timeData, anim, mountPoint })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    protected async eat(action: ActionNode) {
        this.debug("eat")
        let { anim, animMunch, eatCount, munchCount, holdFood } = action.params
        let foodState = this.actionAgent.getState(StateType.HOLD_FOOD)
        if (!foodState && holdFood !== false) {
            this.addFood()
        }

        let type = StateType.EAT
        let { eat, munch } = ActionCfg[type]
        eatCount = cc.js.isNumber(eatCount) ? eatCount : this.randomCount(eat)
        munchCount = cc.js.isNumber(munchCount) ? munchCount : this.randomCount(munch)

        anim = anim || PassengerLifeAnimation.SIT_EAT

        let actionAgent = this.actionAgent
        let carriage = this.carriage
        let role = this.role
        action.onTerminate = () => {
            if (carriage.getID() == CarriageID.DINING) {
                carriage.addAccTotal(role)
            }

            actionAgent.popState(type)
            this.removeFood()
            actionAgent.popState(StateType.HOLD_TABLEWARE)
        }
        let eatTime = actionAgent.getAnimTime(anim)
        let munchTime = actionAgent.getAnimTime(animMunch || PassengerLifeAnimation.SIT_MUNCH)
        let time = eatCount * (eatTime + munchCount * munchTime)

        if (anim != PassengerLifeAnimation.SIT_EAT && !animMunch) { //其他没有配套的咀嚼动画，先这样特殊处理
            munchCount = 0
            time = this.randomTime(ActionCfg[type].time)
            let oneTime = this.actionAgent.getAnimTime(anim)
            time = Math.ceil(time / oneTime) * oneTime
        }

        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(type, { timeData, anim, animMunch, munchCount })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    protected async drink(action: ActionNode) {
        this.debug("drink")
        let { build, anim, animTaste, mountPoint, drinkCount, tasteCount, holdFood } = action.params
        let foodState = this.actionAgent.getState(StateType.HOLD_FOOD)
        if (!foodState && holdFood !== false) {
            let food = this.carriage.getGoodsByType(GoodsType.DRINK).random()
            this.addFood({ slot: 'beizi', food: `take/${food?.takeIcon || "take_trainGoods_2_0"}` })
        }
        anim = anim || PassengerLifeAnimation.SIT_DRINK
        animTaste = animTaste || PassengerLifeAnimation.SIT_TASTE
        let actionAgent = this.actionAgent

        let type = StateType.DRINK
        let { drink, taste } = ActionCfg[type]
        drinkCount = cc.js.isNumber(drinkCount) ? drinkCount : this.randomCount(drink)
        tasteCount = cc.js.isNumber(tasteCount) ? tasteCount : this.randomCount(taste)

        action.onTerminate = () => {
            actionAgent.popState(type)
            this.removeFood()
        }
        let drinkTime = actionAgent.getAnimTime(anim)
        let tasteTime = actionAgent.getAnimTime(animTaste)
        let time = drinkCount * (drinkTime + tasteCount * tasteTime)
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(type, { build, timeData, anim, animTaste, mountPoint, tasteCount })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()

    }

    protected async standPlayAct(action) {
        await action.run(this.toRandomPos)
        await action.run(this.standAct, action.params)
        action.ok()
    }

    protected async standAct(action) {
        action.params = action.param || {}
        action.params.condType = ActCondType.STAND
        await action.run(this.playAct, action.params)
        action.ok()
    }

    //表演
    protected async playAct(action) {
        let { time, anim, condType } = action.params || {}
        let actionAgent = this.actionAgent
        let cfg = this.getActCfg(condType)
        if (cfg) {
            return this.playAct2(action, cfg)
        }

        if (!anim) {
            anim = this.getActAnims(PassengerLifeAnimation.STAND_ACT).random()?.name
        }
        let loop = true
        if (!time) {
            time = this.getActAnimTime(anim) //循环播
            if (!time) { //播一次
                time = this.actionAgent.getAnimTime(anim)
                loop = false
            }
        }
        this.debug("playAct", anim, time, loop)
        action.onTerminate = () => {
            actionAgent.popState(StateType.PLAY_ACT)
        }
        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(StateType.PLAY_ACT, { timeData, anim, loop })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    protected async sitSleep(action) {
        let sleepParams = Object.assign({ anim: PassengerLifeAnimation.SIT_SLEEP }, action.params)
        let build = action.params.build
        sleepParams.build = null
        await action.race([
            [this.sit, { build, time: MAX_VALUE }],
            [this.sleep, sleepParams],
        ])
        action.ok()
    }

    protected async sitEat(action: ActionNode) {
        let { build, time, delayTime, sitAnim, eatAnim, animMunch, holdFood, mountPoint, eatCount, munchCount } = action.params
        let sitAction = action.add(this.sit, { build, time: MAX_VALUE, anim: sitAnim, mountPoint })
        action.onTerminate = () => {
            sitAction.terminate()
        }
        action.run(sitAction)
        if (delayTime > 0) {
            await action.wait(delayTime)
        }
        await action.run(this.eat, { time, anim: eatAnim, animMunch, holdFood, eatCount, munchCount })
        action.onTerminate()
        action.ok()
    }

    protected async sitDrink(action: ActionNode) {
        let { build, time, delayTime, mountPoint, sitAnim } = action.params
        delayTime = delayTime || this.getSitDelayTime()
        let sitAction = action.add(this.sit, { build, time: -1, mountPoint, anim: sitAnim })
        action.onTerminate = () => { sitAction.terminate() }
        action.run(sitAction)
        await action.wait(delayTime)
        await action.run(this.drink, Object.assign({ time }, action.params))
        action.onTerminate()
        action.ok()
    }

    //坐着表演
    protected async sitPlayAct(action: ActionNode) {
        let { build, time, delayTime, mountPoint } = action.params
        delayTime = delayTime || this.getSitDelayTime()
        let sitAction = action.add(this.sit, { build, time: MAX_VALUE, mountPoint })
        action.run(sitAction)
        let anim = this.getActAnims(PassengerLifeAnimation.SIT_ACT).random()?.name
        action.onTerminate = () => {
            sitAction.terminate()
        }
        await action.wait(delayTime)
        await action.run(this.playAct, { anim, condType: ActCondType.SIT })
        action.onTerminate()
        action.ok()
    }

    protected addFood(data?) {
        if (!data) data = {}
        let food = data?.food
        if (!food) {
            let foods = ["apple", "baguette", "bread"]
            let rdIndex = gameHelper.randomByWeight(foods)
            food = foods[rdIndex]
            data.food = food
        }
        let type = StateType.HOLD_FOOD
        this.actionAgent.pushState(type, data)
        this.actionAgent.setMoveAnim(type, data.animMove || PassengerLifeAnimation.WALK_WITH_PLATE)
    }

    protected getJuiceById(id: number) {
        let str = 'juice/'
        switch (id) {
            case 0: return str + 'shui'
            case 1: return str + 'mangguo'
            case 2: return str + 'putao'
            case 3: return str + 'xigua'
        }
    }

    protected removeFood() {
        this.actionAgent.popState(StateType.HOLD_FOOD)
    }

    protected async playAct2(action, data) {
        let actionAgent = this.actionAgent
        action.onTerminate = () => {
            actionAgent.popState(StateType.PLAY_ACT)
        }
        let time = 0
        let cfg = data.act
        if (cfg.start) {
            time += actionAgent.getAnimTime(cfg.start)
        }
        let loopInfos = []
        if (cfg.loop) {
            let count = cfg.count ? this.randomCount(cfg.count) : 1
            while (count--) {
                let loopCount = cfg.loop.count ? this.randomCount(cfg.loop.count) : 1
                time += actionAgent.getAnimTime(cfg.loop.anim) * loopCount

                let needEnd = ut.chance(cfg.s || 0)
                if (needEnd) {
                    time += actionAgent.getAnimsTime(cfg.loopEndAnims || [])
                }

                loopInfos.push({ count: loopCount, needEnd })
            }
        }
        if (cfg.end) {
            time += actionAgent.getAnimsTime(cfg.end)
        }

        let timeData = new TimeStateData().init(time)
        this.actionAgent.pushState(StateType.PLAY_ACT, { timeData, type: data.type || ActType.DEFAULT, loopInfos, cfg })
        await action.wait(timeData)
        action.onTerminate()
        action.ok()
    }

    //------------------------------------------

    //随机移动到一个点
    protected async toRandomPos(action: ActionNode) {
        this.debug('toEmptyPlace')
        let params = action.params || {}
        let pos = this.role.getRandomMovePos()
        let succ = await action.run(this.move, Object.assign(params, { pos }))
        if (succ) {
            action.ok()
        }
    }

    //根据点位移动
    protected async moveByPath(action: ActionNode) {
        let paths = action.params.paths

        for (let { pos, force, dir } of paths) {
            if (dir) {
                this.role.setDir(dir)
            }
            let params = Object.assign(action.params, { pos })
            if (force) {
                await action.run(this.forceMove, params)
            }
            else {
                await action.run(this.move, params)
            }
        }
        action.ok()
    }

    //移动到设施的使用点
    protected async moveToBuild(action: ActionNode) {
        let buildData = action.params
        let { paths } = buildData
        let build: BuildObj = buildData.build
        this.debug('moveToBuild', build.id)

        let dir
        for (let { pos, force, index } of paths) {
            let info = build.getUseByIndex(index)
            pos = pos || info.pos
            force = force || info.force
            dir = info.dir
            if (force) {
                await action.run(this.forceMove, pos)
            }
            else {
                await action.run(this.move, pos)
            }
        }
        if (dir) {
            this.role.setDir(dir)
        }
        action.ok()
    }

    protected async toBuildCommon(action: ActionNode, build: BuildObj, call: Function, paths?, pathOut?) {
        let params = action.params || {}
        let index = 0
        build.setUseLock(true, index, this.role.id)
        action.onTerminate = () => {
            build.setUseLock(false, index, this.role.id)
        }
        if (paths) await action.run(this.moveToBuild, { build, paths: paths })
        await action.run(call, Object.assign(params, { build }))
        action.onTerminate()
        this.actionAgent.addUseBuildRecord(build, index)
        if (pathOut) await action.run(this.moveToBuild, { build, paths: pathOut })
        action.ok()
    }

    //--------------------- 更换车厢 ------------------------------
    protected checkLeave() {
        if (this.carriage != this.role.dorm) return
        let carriage = this.getUseCarriage()
        if (!carriage) return false
        if (!unlockHelper.useRealTime()) return
        let succ = this.checkEvent(PassengerLifeEvent.GOHOME)
        if (succ) {
            this.actionAgent.updateLastEventTime(PassengerLifeEvent.GOHOME)
        }
        return succ
    }

    protected checkGoHome() {
        let dorm = this.role.dorm
        if (!dorm || this.carriage == dorm) return false
        // console.log(this.role.id, this.role.carriageId, this.role.dormId)
        let succ = this.checkEvent(PassengerLifeEvent.GOHOME)
        if (succ) {
            this.actionAgent.updateLastEventTime(PassengerLifeEvent.LEAVE)
            this.actionAgent.updateLastEventTime(PassengerLifeEvent.DINNER)
        }
        return succ
    }

    protected getUseCarriage() { //todo 这里需要区分一下存在性和随机
        let carriages
        if (gameHelper.guide.getGuideInfo(GuideModule.TRIAN_HEAD).isFinish()) {
            carriages = gameHelper.train.getAllCarriages()
        }
        else {
            carriages = gameHelper.train.getCarriages()
        }
        carriages = carriages.filter((carriage: CarriageModel) => {
            if (carriage == this.role.carriage) return false
            if (!carriage.overBuilt) return false //还没建造完成的不去
            if (carriage.isCapFull()) return false //过滤人满的
            if (carriage.isWorkCarriage) return false //过滤工作车厢
            if (carriage.getType() == CarriageType.DORM) return false //过滤寝室
            if (this.role.hates.includes(carriage.getID())) return false //过滤讨厌的
            if (this.actionAgent.carriageCds.find(({ id }) => id == carriage.getID())) return false //过滤还在cd中的
            return true
        })
        if (carriages.length <= 0) return

        let likes = carriages.filter((carriage) => this.role.likes.includes(carriage.getID()))
        if (likes.length > 0) { //有喜欢的优先选喜欢的
            return likes.random()
        }
        let weights = carriages.map(c => 100 - c.getAllPassengerCount() * 10)
        let index = gameHelper.randomByWeight(weights)
        return carriages[index]
    }

    protected async goHome(action) {
        await action.run(this.moveToCarriage, this.role.dorm)
        action.ok()
    }

    protected async toChangeCarriage(action: ActionNode) {
        let carriage = ut.isEmptyObject(action.params) ? this.getUseCarriage() : action.params
        if (!carriage) {
            return action.ok()
        }

        // debug: 强制切换到相邻车厢
        // if (carriage && this.carriage) {
        //     const allCarriages = gameHelper.train.getAllCarriages();
        //     const curIndex = allCarriages.findIndex(c => c === this.carriage);
        //     let neighbor = null;
        //     if (curIndex !== -1) {
        //         // 优先右侧相邻，否则左侧
        //         if (allCarriages[curIndex + 1]) {
        //             neighbor = allCarriages[curIndex + 1];
        //         } else if (allCarriages[curIndex - 1]) {
        //             neighbor = allCarriages[curIndex - 1];
        //         }
        //     }
        //     if (neighbor) {
        //         console.warn('[debug] toChangeCarriage: 强制切换到相邻车厢', this.carriage.getID(), '->', neighbor.getID());
        //         carriage = neighbor;
        //     } else {
        //         console.warn('[debug] toChangeCarriage: 未找到相邻车厢');
        //     }
        // }

        await action.run(this.moveToCarriage, carriage)
        action.ok()
    }

    //去某个车厢
    protected async moveToCarriage(action: ActionNode) {
        let carriage: CarriageModel = action.params
        if (!carriage) {
            twlog.error("moveToCarriage no carriage", this.role.getID())
            return action.ok()
        }
        if (carriage == this.carriage) {
            twlog.error("moveToCarriage same", this.role.id, carriage.getID())
            return action.ok()
        }
        this.debug('moveToCarriage', `${this.carriage.getID()} -> ${carriage.getID()}`)
        let orgCarriage = this.carriage
        carriage.addTempRole(this.role)
        this.actionAgent.targetCarriage = carriage

        action.onTerminate = () => {
            carriage.removeTempRole(this.role)
            this.actionAgent.targetCarriage = null
            this.actionAgent.fromCarriage = null
            this.actionAgent.nextCarriage = null
        }

        let nextCarriage = carriage
        this.actionAgent.fromCarriage = orgCarriage
        this.actionAgent.nextCarriage = nextCarriage

        await action.run(this.exitCarriage, nextCarriage)

        action.ok()

        this.carriage.roleExit(this.role)
        nextCarriage.roleEnter(this.role)
    }

    //移动到准备离开的点
    protected async exitCarriage(action: ActionNode) {
        let target: CarriageModel = action.params
        this.debug('exitCarriage', this.carriage.getID())
        let curIndex = this.carriage.getIndex()
        let targetIndex = target.getIndex()
        let carriageDis = Math.abs(targetIndex - curIndex)
        let isLeft = targetIndex > curIndex

        let map = this.role.getMap()
        let pos = map.getActPointByPixel(this.role.getPosition())
        pos = map.getActPixelByPoint(map.getTransPoint(isLeft))

        if (carriageDis <= 1) { //走过道
            if (target.getID() == CarriageID.HEAD) {
                pos = cc.v2(pos.x, 107)
                await action.run(this.move, pos)
                pos.x = 2662
            }
            else if (this.carriage.getID() == CarriageID.HEAD) {
                pos = cc.v2(0, 0)
                await action.run(this.move, pos)
            }
            else {
                await action.run(this.move, pos) //走到边界点
                let dis = (CARRIAGE_LEN - CARRIAGE_CONTENT_SIZE.width + CARRIAGE_SPACING.x) / 2
                let targetX = (isLeft ? -dis : CARRIAGE_CONTENT_SIZE.width + dis)
                pos = cc.v2(targetX, pos.y)
            }

            if (carriageDis > 0) {
                action.onTerminate = () => {
                    connect.onExit(this.role)
                }
                let connect = gameHelper.train.getConnect(this.carriage, target)
                connect.onEnter(this.role)
            }

            await action.run(this.forceMove, pos)
        }
        else { //瞬移
            if (this.carriage.getID() == CarriageID.HEAD) {
                pos = cc.v2(0, 0)
                await action.run(this.move, pos)
            }
            else {
                await action.run(this.move, pos)
                let offsetX = this.role.viewWidth * this.actionAgent.getScale() //走到看不见角色
                pos.x += (isLeft ? -offsetX : offsetX)
                await action.run(this.forceMove, pos)
            }
        }

        action.ok()
    }

    //刚从别的车厢进来，移动到一个可以走的点
    protected async enterCarriage(action: ActionNode) {
        let fromCarriage = this.actionAgent.fromCarriage
        this.debug('enterCarriage', this.carriage.getID())
        let fromIndex = fromCarriage.getIndex()
        let curIndex = this.carriage.getIndex()
        let isLeft = curIndex > fromIndex
        let carriageDis = Math.abs(curIndex - fromIndex)
        let map = this.carriage.getMap()
        let pos = map.getActPixelByPoint(map.getTransPoint(!isLeft))

        if (carriageDis <= 1) { //走过道过来的
            let fromPos = pos.clone()
            if (fromCarriage.getID() == CarriageID.HEAD) {
                fromPos.set2(2662, 107)
                pos.y = 107
            }
            else if (this.carriage.getID() == CarriageID.HEAD) {
                fromPos.set2(0, 0)
            }
            else {
                let dis = (CARRIAGE_LEN - CARRIAGE_CONTENT_SIZE.width + CARRIAGE_SPACING.x) / 2
                fromPos.x = (isLeft ? CARRIAGE_CONTENT_SIZE.width + dis : -dis)
            }

            let connect = gameHelper.train.getConnect(fromCarriage, this.carriage)
            action.onTerminate = () => {
                connect.onExit(this.role)
            }

            this.role.setPointAndPosition(null, fromPos)
            await action.run(this.forceMove, pos)
            connect.onExit(this.role)
        }
        else { //瞬移过来的
            if (this.carriage.getID() == CarriageID.HEAD) {
                this.role.setPointAndPosition(null, cc.v2(0, 0))
            }
            else {
                let offsetX = this.role.viewWidth * this.actionAgent.getScale()
                let x = pos.x + (isLeft ? offsetX : -offsetX)
                this.role.setPointAndPosition(null, cc.v2(x, pos.y))
                await action.run(this.forceMove, pos) //走到边界点
            }
        }

        action.ok()
    }
    //-------------------------------------------------------------------------

    //---------------去餐厅 ----------------------------------------------------
    protected checkDinner() {
        let id = CarriageID.DINING
        if (this.role.carriageId == id) {
            return false
        }
        let carriage = gameHelper.train.getCarriageById(id) as DiningModel
        if (!gameHelper.train.isCarriageOverBuilt(id)) return
        if (!carriage.canOrderFood()) return
        let succ = this.checkEvent(PassengerLifeEvent.DINNER)
        if (succ) {
            this.actionAgent.updateLastEventTime(PassengerLifeEvent.GOHOME)
        }
        return succ
    }

    protected async toDiningRoom(action) {
        let carriage = gameHelper.train.getCarriageById(CarriageID.DINING)
        await action.run(this.moveToCarriage, carriage)
        action.ok()
    }
    //--------------------------------------------------------------------------

    //---------------------- 移动 ------------------------------
    public async move(action: ActionNode) {
        let params = action.params
        let position: cc.Vec2 = params.pos || params
        this.debug('move', position.x, position.y)
        let succ = false
        if (params.sync) {
            succ = this.moveAgent.searchPathSync(null, position)
        }
        else {
            succ = await action.run(this.searchPath, position)
        }
        if (succ) {
            await this.handleMove(action)
        }
        else {
            twlog.error("searchPath fail", this.role.getPosition().toJson(), " -> ", position.toJson())
            // this.debugE("searchPath fail", this.role.getPosition().toJson(), " -> ", position.toJson())
            await action.run(this.forceMove, params)
        }
        action.ok(true)
    }

    // 强行移动
    public async forceMove(action: ActionNode) {
        this.debug('forceMove')
        let position: cc.Vec2 = action.params.pos || action.params
        this.moveAgent.setMoveTargetPosition(position)
        await this.handleMove(action)
        action.ok()
    }

    //搜索路径
    protected async searchPath(action: ActionNode) {
        this.debug('searchPath')
        let data = action.data
        let position = action.params.pos || action.params
        data.state = 1
        action.onTerminate = () => {
            this.moveAgent.stop()
        }
        this.moveAgent.searchPath(null, position).then(suc => {
            data.state = 2
            data.res = suc
        })
        let waitEnd = (action) => {
            if (data.state == 2) {
                action.ok()
            }
        }
        await action.run(waitEnd)
        action.ok(data.res)
    }

    protected async handleMove(action) {
        let params = action.params
        let anim = params.anim
        this.actionAgent.pushState(StateType.MOVE, { anim })
        let delay = 0, moveTime = 0
        action.onTerminate = () => {
            this.actionAgent.popState(StateType.MOVE)
            this.moveAgent.stop()
        }
        if (RoleJump.includes(this.role.id)) {
            let stepTime = this.actionAgent.getAnimTime(PassengerLifeAnimation.WALK)
            delay = stepTime * 0.27
            moveTime = stepTime - 2 * delay
            this.moveAgent.speedMul = 1.5
            await action.run(this.onMoveStep, { delay, moveTime })
        }
        else {
            await action.run(this.onMove)
        }
        action.onTerminate()
    }

    protected async onMoveStep(action: ActionNode) {
        let delay = action.params.delay
        let moveTime = action.params.moveTime
        let actionAgent = this.actionAgent
        let stepTime = 2 * delay + moveTime
        let anim = action.params.anim

        let timeData = new TimeStateData().init(stepTime)
        actionAgent.pushState(StateType.MOVE_STEP, { anim, timeData })
        action.onTerminate = () => {
            actionAgent.popState(StateType.MOVE_STEP)
        }

        function updateTime(action, dt) {
            let time = action.data.time || action.params
            if (time < dt) {
                dt = time
            }
            time -= dt
            action.data.time = time
            if (timeData.update(dt) || time <= 0) {
                action.ok()
            }
        }

        await action.run(updateTime, delay)
        await action.race([
            [this.onMove],
            [updateTime, moveTime]
        ])
        if (!this.moveAgent.isMoving()) {
            action.onTerminate()
            return action.ok()
        }
        await action.run(updateTime, delay)
        action.onTerminate()
    }

    // 移动中
    protected onMove(action: ActionNode, dt) {
        let moveAgent = this.moveAgent
        // 刷新移动
        moveAgent.updateMove(dt)
        // 移动中
        if (!moveAgent.isMoving()) {
            action.ok()
        }
    }
    //---------------------------------------------------------------

    //---------------------- 并行移动 ------------------------------
    private moveSpawnAction: ActionNode = null
    protected spawnMoveAction(action: ActionNode) {
        if (this.moveSpawnAction) return
        this.moveSpawnAction = action.add(this.updateSpawnMove)
        action.onTerminate = () => { this.moveSpawnAction = null }
        action.run(this.moveSpawnAction)
    }
    protected stopSpawnMove() {
        let act = this.moveSpawnAction
        let ary = act.children
        let one = ary[0]
        let max = ary.length
        if (max > 1) twlog.error('stopSpawnMove max>1', max)
        if (one) {
            one.onTerminate && one.onTerminate()
            one.ok()
        }
    }
    protected startSpawnMove(pos: cc.Vec2) {
        this.moveSpawnAction.params = pos
    }
    protected updateSpawnMove(action: ActionNode) {
        let pos = action.params
        if (!pos) return
        action.params = null
        action.run(this.move, pos)
    }
    protected waitSpawnMoveEnd(action: ActionNode) {
        if (this.isSpawnMoving()) return
        action.ok()
    }
    protected isSpawnMoving() {
        let act = this.moveSpawnAction
        return act && act.children[0] != null
    }
    protected getMoveSpawnPos(): cc.Vec2 {
        let act = this.moveSpawnAction
        if (!act) return
        let pos = act.params
        if (pos) return pos
        let one = act.children[0]
        if (!one) return
        return one.params
    }
    protected async closeToBuild(action: ActionNode) {
        let pos = this.getCloseToPos()
        if (pos.equals(this.role.getPosition())) {
            await action.wait(0.1)
        } else {
            this.startSpawnMove(pos)
            await action.run(this.closeToCheck)
        }
        action.ok()
    }
    protected closeToCheck(action: ActionNode) {
        let pos = this.getMoveSpawnPos()
        if (pos == null) return action.ok()
        if (pos.equals(this.getCloseToPos())) return
        this.stopSpawnMove()
        action.ok()
    }
    protected getCloseToPos() {
        return cc.v2()
    }
    //---------------------------------------------------------------

    protected checkLeaveCarriage() {
        let dic = this.waitRecord
        if (!dic) return false
        return dic.time >= CARRIAGE_MAX_STAY_TIME || dic.over
    }

    protected async checkAndLeaveCarriage(action) {
        if (!this.checkLeaveCarriage()) {
            return
        }
        let carriage = this.getUseCarriage()
        if (carriage) {
            await action.run(this.toChangeCarriage, carriage)
            return action.ok()
        }
    }

    update(dt) {
        if (!this.role) return
        this.actionTree.update(dt)
        if (this.waitRecord?.wait) {
            this.waitRecord.time += dt
        }
    }

    public restart() {
        this.clean()
        this.actionTree.start(this.lifeCycle)
    }

    public pasue() {
        this.actionTree.pause = true
    }

    public resume() {
        this.actionTree.pause = false
    }

    public clean() {
        this.actionTree.terminate()
    }

    public run(func: Function | string, params?) {
        if (typeof func == "string") {
            func = this[func]
        }
        return this.actionTree.start(func, this, params)
    }

}