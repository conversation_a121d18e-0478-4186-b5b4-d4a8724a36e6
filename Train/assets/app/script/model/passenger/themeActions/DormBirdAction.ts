import { BUILD_MOUNT_POINT, PassengerLifeAnimation } from "../../../common/constant/Enums"
import { gameHelper } from "../../../common/helper/GameHelper"
import BuildObj from "../../train/common/BuildObj"
import DormBirdModel, { TragetType } from "../../train/dorm/DormBirdModel"
import DormModel from "../../train/dorm/DormModel"
import ActionTree, { ActionNode } from "../ActionTree"
import { StateType } from "../StateEnum"
import BaseAction from "./BaseAction"

export default class DormBirdAction extends BaseAction {

    protected carriage: DormModel = null
    protected _model: DormBirdModel = null

    public setBy(v: DormBirdModel) {
        this._model = v
        this.actionTree = new ActionTree().init(this)
        this.carriage = v.carriage as DormModel
        this.actionTree.start(this.start)
        return this
    }

    update(dt: number) {
        if (!this._model) return
        this.actionTree.update(dt)
    }

    debug(...params) { console.log(`${this._model.skin} :`, ...params) }

    protected async onBeforeStart(action: ActionNode) {
        // 从外部随机飞入
        if (!this._model.flyIn) {
            await action.run(this.flyIn)
        }
    }

    protected async start(action: ActionNode) {
        this.debug('DormBirdAction start')
        await this.onBeforeStart(action)
        if (action.isOK()) return
        let cfgs = [
            // { act: this.flyIn, check: this.checkStandAct, weight: 25 },
        ]
        await this.runRandomAct(action, cfgs)
        await action.wait(ut.random(1, 3))
    }

    private async flyIn(action: ActionNode) {
        this.debug('flyIn', this._model.skin)

        const availableTargets = [
            { type: TragetType.FLOOR, build: null }
        ]

        let build = this.carriage.getLeftChair()
        if (!this.checkBuildPlay(build)) {
            availableTargets.push({ type: TragetType.LEFT_CHAIR, build })
        }
        build = this.carriage.getRightChair()
        if (!this.checkBuildPlay(build)) {
            availableTargets.push({ type: TragetType.RIGHT_CHAIR, build })
        }
        build = this.carriage.getRightBed()
        if (build) {
            availableTargets.push({ type: TragetType.RIGHT_BED, build })
        }

        const target = availableTargets.random()
        action.terminate = () => this._model.currentTarget = target.type

        if (target.build) {
            await action.run(this.flyToBuild, { build: target.build })
        }
        else {
            await action.run(this.flyToAnyWhere)
        }
        action.terminate()
        action.ok()
    }

    private async flyToBuild(action: ActionNode) {
        if (this._model.isMoving) return
        let build: BuildObj = action.params.build
        this.debug('flyToBuild', build.id)

        action.onTerminate = () => this._model.popState(StateType.BIRD_FLY)

        let handle = () => new Promise((resolve) => {
            this._model.pushState(StateType.BIRD_FLY, { build, resolve })
        })
        await action.waitFunc(handle)
        action.terminate()
        action.ok()
    }

    private async flyToAnyWhere(action: ActionNode) {
        this.debug('flyToAnyWhere')

        await action.wait(5)
        action.ok()
    }

}