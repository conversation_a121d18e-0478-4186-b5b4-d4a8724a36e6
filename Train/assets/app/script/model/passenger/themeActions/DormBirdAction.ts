import { BUILD_MOUNT_POINT, PassengerLifeAnimation } from "../../../common/constant/Enums"
import { gameHelper } from "../../../common/helper/GameHelper"
import BuildObj from "../../train/common/BuildObj"
import DormBirdModel, { TragetType } from "../../train/dorm/DormBirdModel"
import DormModel from "../../train/dorm/DormModel"
import ActionTree, { ActionNode } from "../ActionTree"
import { StateType } from "../StateEnum"
import BaseAction from "./BaseAction"

export default class DormBirdAction extends BaseAction {

    protected carriage: DormModel = null
    protected _model: DormBirdModel = null

    public setBy(v: DormBirdModel) {
        this._model = v
        this.actionTree = new ActionTree().init(this)
        this.carriage = v.carriage as DormModel
        this.actionTree.start(this.lifeCycle)
        return this
    }

    update(dt: number) {
        if (!this._model) return
        this.actionTree.update(dt)
    }

    debug(...params) { console.log(`${this._model.skin} :`, ...params) }

    protected async onBeforeStart(action: ActionNode) {
        await action.wait(ut.random(1, 3))
    }

    protected async lifeCycle(action: ActionNode) {
        await action.run(this.start)
        action.ok()
    }

    protected async start(action: ActionNode) {
        this.debug('DormBirdAction start')
        await this.onBeforeStart(action)
        let cfgs = [
            { act: this.flyIn, weight: 25 },
        ]
        await this.runRandomAct(action, cfgs)
        await action.wait(ut.random(2, 4))
    }

    private async flyIn(action: ActionNode) {
        this.debug('flyIn', this._model.skin)

        const availableTargets = [
            { type: TragetType.FLOOR, build: null, lock: false }
        ]

        const isInitTop = this._model.position.x < 0
        let index = 0
        let build = this.carriage.getLeftChair()
        if (this.checkBuildPlay(build)) {
            build.setUseLock(true, index, -1)
            availableTargets.push({ type: TragetType.LEFT_CHAIR, build, lock: true })
        }
        // build = this.carriage.getRightChair()
        // if (this.checkBuildPlay(build)) {
        //     availableTargets.push({ type: TragetType.RIGHT_CHAIR, build, lock: true })
        // }
        // build = this.carriage.getRightBed()
        // if (build && !isInitTop) {
        //     availableTargets.push({ type: TragetType.RIGHT_BED, build, lock: false })
        // }

        const target = availableTargets.filter(t => !t.lock || t.type != this._model.currentTarget).random()
        for (const t of availableTargets) {
            if (t.lock && t !== target) {
                t.build.setUseLock(false, index, -1)
            }
        }
        action.onTerminate = () => {
            if (target.lock) target.build.setUseLock(false, index, -1)
            this._model.currentTarget = target.type
        }

        if (target.build) {
            await action.run(this.flyToBuild, { build: target.build })
        }
        else {
            await action.run(this.flyToAnyWhere)
        }
        action.onTerminate()
        action.ok()
    }

    private async flyToBuild(action: ActionNode) {
        if (this._model.isMoving) return
        let build: BuildObj = action.params.build
        this.debug('flyToBuild', build.id)

        action.onTerminate = () => this._model.popState(StateType.BIRD_FLY)

        let handle = () => new Promise((resolve) => {
            this._model.pushState(StateType.BIRD_FLY, { build, resolve })
        })
        await action.waitFunc(handle)
        action.onTerminate()
        action.ok()
    }

    private async flyToAnyWhere(action: ActionNode) {
        this.debug('flyToAnyWhere')

        await action.wait(5)
        action.ok()
    }

}